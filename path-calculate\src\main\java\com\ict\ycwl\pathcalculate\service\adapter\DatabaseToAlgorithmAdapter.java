package com.ict.ycwl.pathcalculate.service.adapter;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult;
import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import com.ict.ycwl.pathcalculate.mapper.*;
import com.ict.ycwl.pathcalculate.mapper.TravelTimeMapper;
import com.ict.ycwl.pathcalculate.pojo.*;
import com.ict.ycwl.pathcalculate.pojo.TravelTime;
import com.ict.ycwl.pathcalculate.service.RouteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据库到算法的数据转换适配器
 * 负责将数据库中的数据转换为算法需要的格式，以及将算法结果转换为前端需要的格式
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-11
 */
@Slf4j
@Component
public class DatabaseToAlgorithmAdapter {

    @Autowired
    private AccumulationMapper accumulationMapper;
    
    @Autowired
    private TransitDepotMapper transitDepotMapper;
    
    @Autowired
    private GroupMapper groupMapper;
    
    @Autowired
    private PointDistanceMapper pointDistanceMapper;

    @Autowired
    private TravelTimeMapper travelTimeMapper;

    @Autowired
    private RouteService routeService;

    /**
     * 从数据库加载数据并转换为算法请求格式
     * 
     * @return PathPlanningRequest 算法请求对象
     */
    public PathPlanningRequest loadDataFromDatabase() {
        log.info("开始从数据库加载数据并转换为算法格式");
        
        try {
            // 1. 加载聚集区数据
            List<com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation> algorithmAccumulations = loadAccumulations();
            log.info("加载聚集区数据: {} 个", algorithmAccumulations.size());
            
            // 2. 加载中转站数据
            List<com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot> algorithmTransitDepots = loadTransitDepots();
            log.info("加载中转站数据: {} 个", algorithmTransitDepots.size());
            
            // 3. 加载班组数据
            List<Team> teams = loadTeams();
            log.info("加载班组数据: {} 个", teams.size());
            
            // 4. 加载时间矩阵数据
            Map<String, TimeInfo> timeMatrix = loadTimeMatrix();
            log.info("加载时间矩阵数据: {} 条记录", timeMatrix.size());
            
            PathPlanningRequest request = PathPlanningRequest.builder()
                    .accumulations(algorithmAccumulations)
                    .transitDepots(algorithmTransitDepots)
                    .teams(teams)
                    .timeMatrix(timeMatrix)
                    .build();
            
            log.info("数据转换完成，构建PathPlanningRequest成功");
            return request;
            
        } catch (Exception e) {
            log.error("从数据库加载数据失败", e);
            throw new RuntimeException("数据加载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载聚集区数据
     */
    private List<com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation> loadAccumulations() {
        // 获取所有未删除的聚集区
        QueryWrapper<com.ict.ycwl.pathcalculate.pojo.Accumulation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<com.ict.ycwl.pathcalculate.pojo.Accumulation> dbAccumulations = accumulationMapper.selectList(queryWrapper);
        
        return dbAccumulations.stream()
                .filter(acc -> acc.getTransitDepotId() != null) // 过滤掉没有分配中转站的聚集区
                .map(this::convertAccumulation)
                .collect(Collectors.toList());
    }

    /**
     * 转换聚集区数据格式
     */
    private com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation convertAccumulation(
            com.ict.ycwl.pathcalculate.pojo.Accumulation dbAccumulation) {
        
        // 计算配送时间（这里使用默认值，实际项目中可能需要从其他表获取）
        Double deliveryTime = calculateDeliveryTime(dbAccumulation);
        
        return com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation.builder()
                .accumulationId(dbAccumulation.getAccumulationId())
                .accumulationName(dbAccumulation.getAccumulationName())
                .longitude(dbAccumulation.getLongitude())
                .latitude(dbAccumulation.getLatitude())
                .transitDepotId(dbAccumulation.getTransitDepotId())
                .deliveryTime(deliveryTime)
                .build();
    }

    /**
     * 计算聚集区配送时间
     * 这里使用简化的计算方式，实际项目中可能需要更复杂的逻辑
     */
    private Double calculateDeliveryTime(com.ict.ycwl.pathcalculate.pojo.Accumulation dbAccumulation) {
        // 默认配送时间15分钟，可以根据实际业务逻辑调整
        return 15.0;
    }

    /**
     * 加载中转站数据
     */
    private List<com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot> loadTransitDepots() {
        // 获取所有启用的中转站
        QueryWrapper<com.ict.ycwl.pathcalculate.pojo.TransitDepot> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "1"); // 启用状态
        queryWrapper.eq("is_delete", 0); // 未删除
        List<com.ict.ycwl.pathcalculate.pojo.TransitDepot> dbTransitDepots = transitDepotMapper.selectList(queryWrapper);
        
        return dbTransitDepots.stream()
                .map(this::convertTransitDepot)
                .collect(Collectors.toList());
    }

    /**
     * 转换中转站数据格式
     */
    private com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot convertTransitDepot(
            com.ict.ycwl.pathcalculate.pojo.TransitDepot dbTransitDepot) {
        
        // 计算路线数量（这里使用默认值，实际项目中可能需要从配置或其他表获取）
        Integer routeCount = calculateRouteCount(dbTransitDepot);
        
        return com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot.builder()
                .transitDepotId(dbTransitDepot.getTransitDepotId())
                .transitDepotName(dbTransitDepot.getTransitDepotName())
                .longitude(Double.parseDouble(dbTransitDepot.getLongitude()))
                .latitude(Double.parseDouble(dbTransitDepot.getLatitude()))
                .groupId(dbTransitDepot.getGroupId())
                .routeCount(routeCount)
                .build();
    }

    /**
     * 计算中转站的路线数量
     * 这里使用简化的计算方式，实际项目中可能需要更复杂的逻辑
     */
    private Integer calculateRouteCount(com.ict.ycwl.pathcalculate.pojo.TransitDepot dbTransitDepot) {
        // 根据中转站下的聚集区数量估算路线数量
        QueryWrapper<com.ict.ycwl.pathcalculate.pojo.Accumulation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("transit_depot_id", dbTransitDepot.getTransitDepotId());
        queryWrapper.eq("is_delete", 0);
        Long accumulationCount = accumulationMapper.selectCount(queryWrapper);
        
        // 每5个聚集区分配一条路线，最少1条，最多10条
        int routeCount = Math.max(1, Math.min(10, (int) Math.ceil(accumulationCount / 5.0)));
        return routeCount;
    }

    /**
     * 加载班组数据
     */
    private List<Team> loadTeams() {
        // 获取所有班组
        List<Group> dbGroups = groupMapper.selectList(null);
        
        return dbGroups.stream()
                .map(this::convertTeam)
                .collect(Collectors.toList());
    }

    /**
     * 转换班组数据格式
     */
    private Team convertTeam(Group dbGroup) {
        // 获取班组下的所有中转站ID
        QueryWrapper<com.ict.ycwl.pathcalculate.pojo.TransitDepot> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", dbGroup.getGroupId());
        queryWrapper.eq("status", "1");
        queryWrapper.eq("is_delete", 0);
        List<com.ict.ycwl.pathcalculate.pojo.TransitDepot> transitDepots = transitDepotMapper.selectList(queryWrapper);
        
        List<Long> transitDepotIds = transitDepots.stream()
                .map(com.ict.ycwl.pathcalculate.pojo.TransitDepot::getTransitDepotId)
                .collect(Collectors.toList());
        
        return Team.builder()
                .teamId(dbGroup.getGroupId())
                .teamName(dbGroup.getGroupName())
                .transitDepotIds(transitDepotIds)
                .build();
    }

    /**
     * 加载时间矩阵数据 - 🎯 修复：使用travel_time表替代point_distance表
     */
    private Map<String, TimeInfo> loadTimeMatrix() {
        log.info("🎯 从travel_time表加载时间矩阵数据（280万条记录）");

        // 获取所有行驶时间数据
        List<TravelTime> travelTimes = travelTimeMapper.selectList(null);
        log.info("从travel_time表查询到 {} 条记录", travelTimes.size());

        Map<String, TimeInfo> timeMatrix = new HashMap<>();

        for (TravelTime tt : travelTimes) {
            try {
                if (tt.getLongitudeStart() != null && tt.getLatitudeStart() != null &&
                    tt.getLongitudeEnd() != null && tt.getLatitudeEnd() != null &&
                    tt.getTravelTime() != null && tt.getTravelTime() > 0) {

                    double fromLng = Double.parseDouble(tt.getLongitudeStart());
                    double fromLat = Double.parseDouble(tt.getLatitudeStart());
                    double toLng = Double.parseDouble(tt.getLongitudeEnd());
                    double toLat = Double.parseDouble(tt.getLatitudeEnd());

                    // 直接使用travel_time表中的时间数据（已经是分钟单位）
                    double travelTimeMinutes = tt.getTravelTime();

                    String key = String.format("%f,%f->%f,%f", fromLng, fromLat, toLng, toLat);

                    TimeInfo timeInfo = TimeInfo.builder()
                            .fromLongitude(fromLng)
                            .fromLatitude(fromLat)
                            .toLongitude(toLng)
                            .toLatitude(toLat)
                            .travelTime(travelTimeMinutes)
                            .distance(0.0) // 距离字段设为0，新算法主要使用时间
                            .build();

                    timeMatrix.put(key, timeInfo);
                }
            } catch (Exception e) {
                log.warn("解析travel_time数据失败: {}", tt, e);
            }
        }

        log.info("✅ 时间矩阵加载完成，共 {} 条记录（从travel_time表）", timeMatrix.size());
        return timeMatrix;
    }

    /**
     * 解析坐标字符串
     * 支持格式: "longitude,latitude" 或 "longitude latitude"
     */
    private String[] parseCoordinates(String coordStr) {
        if (coordStr == null || coordStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试用逗号分割
            if (coordStr.contains(",")) {
                return coordStr.split(",");
            }
            // 尝试用空格分割
            else if (coordStr.contains(" ")) {
                return coordStr.split("\\s+");
            }
            // 其他格式暂不支持
            else {
                return null;
            }
        } catch (Exception e) {
            log.warn("解析坐标字符串失败: {}", coordStr, e);
            return null;
        }
    }

    /**
     * 将算法结果转换为前端需要的ResultRoute格式
     * 
     * @param algorithmResult 算法计算结果
     * @param apiKey API密钥（用于计算工作时间等）
     * @return List<ResultRoute> 前端需要的结果格式
     */
    public List<ResultRoute> convertAlgorithmResult(PathPlanningResult algorithmResult, String apiKey) {
        log.info("开始转换算法结果为前端格式");
        
        if (!algorithmResult.isSuccess() || algorithmResult.getRoutes() == null) {
            log.warn("算法执行失败或结果为空: {}", algorithmResult.getErrorMessage());
            return new ArrayList<>();
        }
        
        List<ResultRoute> resultRoutes = new ArrayList<>();
        
        for (com.ict.ycwl.pathcalculate.algorithm.entity.RouteResult algorithmRoute : algorithmResult.getRoutes()) {
            try {
                ResultRoute resultRoute = convertSingleRoute(algorithmRoute, apiKey);
                if (resultRoute != null) {
                    resultRoutes.add(resultRoute);
                }
            } catch (Exception e) {
                log.error("转换路线失败: {}", algorithmRoute.getRouteName(), e);
            }
        }
        
        log.info("算法结果转换完成，生成 {} 条路线", resultRoutes.size());
        return resultRoutes;
    }

    /**
     * 转换单条路线结果
     */
    private ResultRoute convertSingleRoute(com.ict.ycwl.pathcalculate.algorithm.entity.RouteResult algorithmRoute, String apiKey) {
        ResultRoute resultRoute = new ResultRoute();
        
        // 基本信息
        resultRoute.setRouteId(algorithmRoute.getRouteId());
        resultRoute.setRouteName(algorithmRoute.getRouteName());
        resultRoute.setTransitDepotId(algorithmRoute.getTransitDepotId());
        
        // 转换坐标点串
        List<LngAndLat> polyline = convertCoordinatePoints(algorithmRoute.getPolyline());
        resultRoute.setPolyline(polyline);
        
        // 转换凸包
        List<LngAndLat> convex = convertCoordinatePoints(algorithmRoute.getConvexHull());
        resultRoute.setConvex(convex);
        
        // 工作时间
        if (algorithmRoute.getTotalWorkTime() != null) {
            resultRoute.setWorkTime(BigDecimal.valueOf(algorithmRoute.getTotalWorkTime()));
        }
        
        // 设置其他字段的默认值
        resultRoute.setDistance("0.00"); // 距离需要单独计算
        resultRoute.setCargoWeight("0.00"); // 载货量需要单独计算
        resultRoute.setCreateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        resultRoute.setUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        resultRoute.setDelete(false);
        
        // 生成路线名称（如果算法没有提供）
        if (resultRoute.getRouteName() == null || resultRoute.getRouteName().isEmpty()) {
            resultRoute.setRouteName(generateRouteName(algorithmRoute.getTransitDepotId()));
        }
        
        return resultRoute;
    }

    /**
     * 转换坐标点格式
     */
    private List<LngAndLat> convertCoordinatePoints(List<CoordinatePoint> coordinatePoints) {
        if (coordinatePoints == null) {
            return new ArrayList<>();
        }
        
        return coordinatePoints.stream()
                .map(cp -> new LngAndLat(cp.getLongitude(), cp.getLatitude()))
                .collect(Collectors.toList());
    }

    /**
     * 生成路线名称
     */
    private String generateRouteName(Long transitDepotId) {
        try {
            com.ict.ycwl.pathcalculate.pojo.TransitDepot transitDepot = transitDepotMapper.selectById(transitDepotId);
            String transitDepotName = transitDepot != null ? transitDepot.getTransitDepotName() : "未知中转站";
            
            LocalDateTime currentTime = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
            String formattedTime = currentTime.format(formatter);
            
            return String.format("%s-算法路线-%s", transitDepotName, formattedTime);
        } catch (Exception e) {
            log.warn("生成路线名称失败", e);
            return "算法路线-" + System.currentTimeMillis();
        }
    }
}
