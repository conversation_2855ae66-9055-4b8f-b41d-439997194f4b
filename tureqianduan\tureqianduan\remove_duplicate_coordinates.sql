-- 删除重复坐标的商铺记录
-- 策略：对于每个重复的坐标，保留store_id最小的记录，删除其他记录

-- 首先查看重复坐标的情况（执行前先了解数据）
SELECT 
    '执行删除前的重复坐标统计' as 说明,
    COUNT(*) as 重复坐标组数,
    SUM(商铺数量) as 涉及商铺总数,
    SUM(商铺数量) - COUNT(*) as 将要删除的商铺数
FROM (
    SELECT 
        longitude, 
        latitude, 
        COUNT(*) as 商铺数量
    FROM store 
    WHERE is_delete = 0 
    GROUP BY longitude, latitude 
    HAVING COUNT(*) > 1
) duplicate_coords;

-- 查看具体的重复坐标详情
SELECT 
    longitude as 经度,
    latitude as 纬度,
    COUNT(*) as 商铺数量,
    GROUP_CONCAT(
        CONCAT(store_id, ':', customer_code, '(', store_address, ')') 
        ORDER BY store_id 
        SEPARATOR ' | '
    ) as 商铺详情_按store_id排序
FROM store 
WHERE is_delete = 0 
GROUP BY longitude, latitude 
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC, longitude, latitude;

-- 特别查看B07188和B07049的情况
SELECT 
    store_id,
    customer_code as 商铺编码,
    store_name as 商铺名称,
    store_address as 商铺地址,
    longitude as 经度,
    latitude as 纬度,
    accumulation_id as 聚集区ID
FROM store 
WHERE customer_code IN ('B07188', 'B07049') 
   AND is_delete = 0
ORDER BY longitude, latitude, store_id;

-- ==========================================
-- 重要：以下是删除操作，请谨慎执行！
-- ==========================================

-- 备份操作：创建备份表（可选，建议执行）
-- CREATE TABLE store_backup_before_dedup AS SELECT * FROM store WHERE is_delete = 0;

-- 删除重复坐标的商铺（保留每个坐标位置store_id最小的记录）
DELETE s1 FROM store s1
INNER JOIN store s2 
WHERE s1.longitude = s2.longitude 
  AND s1.latitude = s2.latitude
  AND s1.store_id > s2.store_id  -- 删除store_id较大的记录
  AND s1.is_delete = 0 
  AND s2.is_delete = 0;

-- 验证删除结果
SELECT 
    '删除后的重复坐标统计' as 说明,
    COUNT(*) as 重复坐标组数
FROM (
    SELECT 
        longitude, 
        latitude, 
        COUNT(*) as 商铺数量
    FROM store 
    WHERE is_delete = 0 
    GROUP BY longitude, latitude 
    HAVING COUNT(*) > 1
) remaining_duplicates;

-- 查看删除后B07188和B07049的情况
SELECT 
    store_id,
    customer_code as 商铺编码,
    store_name as 商铺名称,
    store_address as 商铺地址,
    longitude as 经度,
    latitude as 纬度,
    accumulation_id as 聚集区ID
FROM store 
WHERE customer_code IN ('B07188', 'B07049') 
   AND is_delete = 0
ORDER BY longitude, latitude, store_id;

-- 查看坐标 113.310826, 25.013573 删除后的情况
SELECT 
    store_id,
    customer_code as 商铺编码,
    store_name as 商铺名称,
    store_address as 商铺地址,
    longitude as 经度,
    latitude as 纬度
FROM store 
WHERE longitude = 113.310826 
   AND latitude = 25.013573 
   AND is_delete = 0
ORDER BY store_id;
