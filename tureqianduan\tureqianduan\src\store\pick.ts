import { defineStore } from "pinia";
import type {
  IPickupUserCalculateData,
  IPickupUserGetListData,
  IPickupUserUpdateData,
  IPickupLogImport,
  IFromDownload,
  ILogDownload,
  IDeleteLog,
  IUnassignedList,
} from "@/types/pickup";
import {
  importPickup,
  exportPickup,
  getPickupList,
  updatePickup,
  calculatePickup,
  updateParams,
  importPickupLog,
  downloadTemplate,
  downloadLog,
  deleteLog,
  getSelect,
  unassignedList,
  getParamsList,
  getMapData,
} from "@/service/modules/pick";

export const usePickStore = defineStore("pick", () => {
  // 加载中
  const loading = ref<boolean>(false);
  //导入用户
  async function importUser(data: FormData, config: any) {
    const res = await importPickup(data, config);
    return res;
  }

  //导出表格
  async function exportUser() {
    const res = await exportPickup();
    return res;
  }

  //获取用户列表
  async function getUserList(params: IPickupUserGetListData) {
    loading.value = true;
    const res = await getPickupList(params);
    loading.value = false;
    return res.data;
  }

  //修改用户信息
  async function updateUser(params: IPickupUserUpdateData) {
    const res = await updatePickup(params);
    return res;
  }

  //计算用户
  async function calculateUser(params: any) {
    loading.value = true;
    const res = await calculatePickup(params);
    loading.value = false;
    return res;
  }

  //导入日志
  async function importUserLog(params: IPickupLogImport) {
    const res = await importPickupLog(params);
    return res.data;
  }

  //  下载表格
  async function downloadNullForm(params: IFromDownload) {
    const res = await downloadTemplate(params);
    return res;
  }

  //下载日志
  async function downloadUserLog(params: ILogDownload) {
    const res = await downloadLog(params);
    return res;
  }

  //删除日志
  async function deleteCarLog(params: IDeleteLog) {
    const res = await deleteLog(params);
    return res;
  }

  //获取下拉框
  async function getSelectList() {
    const res = await getSelect();
    return res.data;
  }

  //待分配
  async function beAssigned(params: IUnassignedList) {
    const res = await unassignedList(params);
    return res;
  }

  //ditu
  async function getMap() {
    const res = await getMapData();
    return res.data;
  }

  //更新参数
  async function updateParamsList(params: any) {
    const res = await updateParams(params);
    return res;
  }

  //参数列表
  async function getParams() {
    const res = await getParamsList();
    return res.data;
  }

  return {
    importUser,
    exportUser,
    getUserList,
    updateUser,
    calculateUser,
    importUserLog,
    downloadNullForm,
    updateParamsList,
    downloadUserLog,
    deleteCarLog,
    getSelectList,
    beAssigned,
    getMap,
    getParams,
    loading,
  };
});
