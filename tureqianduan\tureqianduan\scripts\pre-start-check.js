/**
 * 启动前检查脚本
 * 在开发服务器启动前检查必要的配置
 */

const fs = require('fs');
const path = require('path');

function checkEnvironmentFile() {
    console.log('🔍 检查环境变量文件...');
    
    const envFile = '.env.prd';
    
    if (!fs.existsSync(envFile)) {
        console.error(`❌ 环境文件不存在: ${envFile}`);
        console.log('正在创建默认环境文件...');
        
        const defaultEnvContent = `VITE_BASE_URL="http://localhost:8080"
VITE_MAP_KEY = '004bb8268ef0cff3264e9b2a8816e29c'
VITE_MAP_API_KEY = '3729e38b382749ba3a10bae7539e0d9a'
VITE_SECURITY_CODE = '92707992ffdad18bfbf2f31e9bd158bd'
`;
        
        fs.writeFileSync(envFile, defaultEnvContent);
        console.log(`✅ 已创建 ${envFile}`);
    } else {
        console.log(`✅ 环境文件存在: ${envFile}`);
        
        // 检查必要的环境变量
        const content = fs.readFileSync(envFile, 'utf8');
        const requiredVars = ['VITE_MAP_KEY', 'VITE_SECURITY_CODE', 'VITE_BASE_URL'];
        
        for (const varName of requiredVars) {
            if (content.includes(varName)) {
                console.log(`  ✅ ${varName}: 已配置`);
            } else {
                console.error(`  ❌ ${varName}: 未配置`);
            }
        }
    }
}

function checkMapFiles() {
    console.log('\n🔍 检查地图相关文件...');
    
    const requiredFiles = [
        'src/utils/getMapKey.ts',
        'src/utils/modifyUserAgent.ts',
        'src/utils/mapBluePoint.ts',
        'src/pages/Home/Computer/Route/Route.vue'
    ];
    
    let allFilesExist = true;
    
    for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
            console.log(`  ✅ ${file}`);
        } else {
            console.error(`  ❌ ${file} 不存在`);
            allFilesExist = false;
        }
    }
    
    return allFilesExist;
}

function checkDependencies() {
    console.log('\n🔍 检查依赖包...');
    
    const packageJsonPath = 'package.json';
    
    if (!fs.existsSync(packageJsonPath)) {
        console.error('❌ package.json 不存在');
        return false;
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const requiredDeps = [
        '@amap/amap-jsapi-loader',
        'vue',
        'element-plus'
    ];
    
    let allDepsExist = true;
    
    for (const dep of requiredDeps) {
        const inDeps = packageJson.dependencies && packageJson.dependencies[dep];
        const inDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
        
        if (inDeps || inDevDeps) {
            console.log(`  ✅ ${dep}`);
        } else {
            console.error(`  ❌ ${dep} 未安装`);
            allDepsExist = false;
        }
    }
    
    return allDepsExist;
}

function createDebugInfo() {
    console.log('\n📝 创建调试信息文件...');
    
    const debugInfo = {
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
        platform: process.platform,
        cwd: process.cwd(),
        env: {
            NODE_ENV: process.env.NODE_ENV,
            VITE_MODE: process.env.VITE_MODE
        }
    };
    
    fs.writeFileSync('debug-info.json', JSON.stringify(debugInfo, null, 2));
    console.log('✅ 调试信息已保存到 debug-info.json');
}

function main() {
    console.log('🚀 启动前检查开始...\n');
    
    checkEnvironmentFile();
    const filesOk = checkMapFiles();
    const depsOk = checkDependencies();
    createDebugInfo();
    
    console.log('\n📊 检查结果:');
    console.log('='.repeat(40));
    
    if (filesOk && depsOk) {
        console.log('✅ 所有检查通过，可以启动开发服务器');
        console.log('\n💡 提示:');
        console.log('  - 如果地图仍然无法显示，请访问 http://localhost:5173/debug-map.html');
        console.log('  - 检查浏览器控制台是否有错误信息');
        console.log('  - 确保后端服务已启动 (http://localhost:8080)');
    } else {
        console.error('❌ 检查未通过，请解决上述问题后重新启动');
        process.exit(1);
    }
    
    console.log('\n✨ 检查完成！');
}

if (require.main === module) {
    main();
}

module.exports = { checkEnvironmentFile, checkMapFiles, checkDependencies };
