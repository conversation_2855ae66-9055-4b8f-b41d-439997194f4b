2025-08-14 22:46:27.209 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 开始路径规划算法，聚集区数量: 1821, 中转站数量: 6, 调试会话: debug_20250814_224627
2025-08-14 22:46:27.209 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段1：数据验证和预处理
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 数据预处理完成，构建了 6 个中转站分组
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 开始聚类阶段，调试会话ID: debug_20250814_224627
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段2：初始路线分配
2025-08-14 22:46:30.265 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 新丰县中转站 分配 118 个聚集区到 10 条路线
2025-08-14 22:46:30.310 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.310 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 坪石镇中转站 分配 237 个聚集区到 10 条路线
2025-08-14 22:46:30.347 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.347 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 翁源县中转站 分配 168 个聚集区到 10 条路线
2025-08-14 22:46:30.375 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.375 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 马市烟叶工作站 分配 328 个聚集区到 10 条路线
2025-08-14 22:46:30.473 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.473 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组一物流配送中心 分配 497 个聚集区到 10 条路线
2025-08-14 22:46:30.525 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.525 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 为中转站 班组二物流配送中心 分配 473 个聚集区到 10 条路线
2025-08-14 22:46:30.554 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 检查聚类二次优化条件 - 启用开关: false, 优化器: 已注入
2025-08-14 22:46:30.554 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线分配完成，总路线数: 125
2025-08-14 22:46:30.554 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 准备导出聚类结果，路线聚类数: 6, 会话ID: debug_20250814_224627
2025-08-14 22:46:30.574 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 🔍 聚类结果导出完成
2025-08-14 22:46:30.574 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3：路线内序列优化
2025-08-14 22:46:33.302 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 10/125
2025-08-14 22:46:36.007 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 20/125
2025-08-14 22:46:38.564 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 30/125
2025-08-14 22:46:41.190 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 40/125
2025-08-14 22:46:43.747 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 50/125
2025-08-14 22:46:46.414 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 60/125
2025-08-14 22:46:49.132 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 70/125
2025-08-14 22:46:51.727 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 80/125
2025-08-14 22:46:54.582 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 90/125
2025-08-14 22:46:54.872 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 100/125
2025-08-14 22:46:56.014 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 110/125
2025-08-14 22:46:56.267 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - TSP优化进度: 120/125
2025-08-14 22:46:57.060 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路线序列优化完成，优化了 125 条路线
2025-08-14 22:46:57.062 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段3.5：TSP后约束优化 - 使用第三方高性能库进行动态调整
2025-08-14 22:46:57.080 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [TSP后优化部分成功] 第三方库优化完成，部分约束可能仍然违反
2025-08-14 22:46:57.080 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 📊 [约束统计] 总路线: 125, 超450分钟路线: 54, 超30分钟差距中转站: 6
2025-08-14 22:46:57.080 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils -    最长路线时间: {:.1f}分钟, 最大时间差距: {:.1f}分钟
2025-08-14 22:46:57.080 WARN  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - ⚠️ [约束违反] 仍有约束违反，需要进一步优化算法参数
2025-08-14 22:46:57.082 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段4：凸包生成与冲突解决
2025-08-14 22:46:57.098 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 凸包冲突解决完成，解决了 0 个冲突
2025-08-14 22:46:57.100 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段5：多层级时间均衡
2025-08-14 22:48:10.967 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 时间均衡完成，路线调整: 191, 中转站调整: 0
2025-08-14 22:48:10.967 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 阶段6：构建最终结果
2025-08-14 22:48:10.972 INFO  c.i.ycwl.pathcalculate.algorithm.PathPlanningUtils - 路径规划算法执行完成，耗时: 103759ms, 生成路线: 125, 总工作时间: 91937.5分钟
