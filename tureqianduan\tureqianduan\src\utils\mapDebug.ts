/**
 * 地图调试工具
 * 用于诊断地图初始化和交互问题
 */

import { MAP_KEY, SECURITY_CODE } from './getMapKey';

export class MapDebugger {
  private static instance: MapDebugger;
  private debugInfo: any = {};

  static getInstance(): MapDebugger {
    if (!MapDebugger.instance) {
      MapDebugger.instance = new MapDebugger();
    }
    return MapDebugger.instance;
  }

  /**
   * 检查环境变量
   */
  checkEnvironment(): boolean {
    console.group('🔍 地图环境检查');
    
    const checks = {
      MAP_KEY: MAP_KEY,
      SECURITY_CODE: SECURITY_CODE,
      NODE_ENV: import.meta.env.MODE,
      BASE_URL: import.meta.env.VITE_BASE_URL
    };

    let allValid = true;
    
    for (const [key, value] of Object.entries(checks)) {
      if (!value || value === 'undefined') {
        console.error(`❌ ${key}: 未设置或为空`);
        allValid = false;
      } else {
        const displayValue = key.includes('KEY') || key.includes('CODE') ? 
          value.substring(0, 8) + '...' : value;
        console.log(`✅ ${key}: ${displayValue}`);
      }
    }

    this.debugInfo.environment = { checks, valid: allValid };
    console.groupEnd();
    return allValid;
  }

  /**
   * 检查高德地图API加载状态
   */
  checkAmapAPI(): boolean {
    console.group('🗺️ 高德地图API检查');
    
    const isLoaded = typeof window.AMap !== 'undefined';
    const hasSecurityConfig = typeof window._AMapSecurityConfig !== 'undefined';
    
    console.log(`AMap对象: ${isLoaded ? '✅ 已加载' : '❌ 未加载'}`);
    console.log(`安全配置: ${hasSecurityConfig ? '✅ 已设置' : '❌ 未设置'}`);
    
    if (isLoaded) {
      console.log(`AMap版本: ${window.AMap.version || '未知'}`);
    }

    this.debugInfo.amapAPI = { isLoaded, hasSecurityConfig };
    console.groupEnd();
    return isLoaded && hasSecurityConfig;
  }

  /**
   * 检查地图容器
   */
  checkMapContainer(containerId: string): boolean {
    console.group('📦 地图容器检查');
    
    const container = document.getElementById(containerId);
    const exists = !!container;
    
    if (exists) {
      const rect = container.getBoundingClientRect();
      const hasSize = rect.width > 0 && rect.height > 0;
      
      console.log(`✅ 容器存在: ${containerId}`);
      console.log(`尺寸: ${rect.width}x${rect.height} ${hasSize ? '✅' : '❌'}`);
      console.log(`可见性: ${container.offsetParent !== null ? '✅' : '❌'}`);
      
      this.debugInfo.container = { exists, hasSize, rect };
      console.groupEnd();
      return hasSize;
    } else {
      console.error(`❌ 容器不存在: ${containerId}`);
      this.debugInfo.container = { exists: false };
      console.groupEnd();
      return false;
    }
  }

  /**
   * 测试地图初始化
   */
  async testMapInitialization(containerId: string): Promise<boolean> {
    console.group('🚀 地图初始化测试');
    
    try {
      if (!window.AMap) {
        throw new Error('AMap未加载');
      }

      const map = new window.AMap.Map(containerId, {
        zoom: 10,
        center: [113.767587, 24.718014], // 韶关市中心
        viewMode: '3D'
      });

      // 测试基本功能
      const marker = new window.AMap.Marker({
        position: [113.767587, 24.718014],
        title: '测试标记'
      });
      
      map.add(marker);
      
      console.log('✅ 地图初始化成功');
      console.log('✅ 标记添加成功');
      
      this.debugInfo.initialization = { success: true, map, marker };
      console.groupEnd();
      return true;
      
    } catch (error) {
      console.error('❌ 地图初始化失败:', error);
      this.debugInfo.initialization = { success: false, error: error.message };
      console.groupEnd();
      return false;
    }
  }

  /**
   * 检查后端API连接
   */
  async checkBackendAPI(): Promise<boolean> {
    console.group('🔗 后端API检查');
    
    const baseURL = import.meta.env.VITE_BASE_URL || 'http://localhost:8080';
    const apis = [
      '/pathcalculate/path/getTransitDepotName',
      '/pathcalculate/path/getAllColourConvex',
      '/pathcalculate/path/getConvexPoint'
    ];

    let allSuccess = true;
    const results = {};

    for (const api of apis) {
      try {
        const response = await fetch(baseURL + api);
        const success = response.ok;
        
        console.log(`${success ? '✅' : '❌'} ${api}: HTTP ${response.status}`);
        results[api] = { success, status: response.status };
        
        if (!success) allSuccess = false;
        
      } catch (error) {
        console.error(`❌ ${api}: 网络错误 - ${error.message}`);
        results[api] = { success: false, error: error.message };
        allSuccess = false;
      }
    }

    this.debugInfo.backendAPI = { allSuccess, results };
    console.groupEnd();
    return allSuccess;
  }

  /**
   * 运行完整诊断
   */
  async runFullDiagnosis(containerId: string = 'container'): Promise<any> {
    console.group('🔍 地图完整诊断');
    console.log('开始诊断地图功能...');
    
    const results = {
      environment: await this.checkEnvironment(),
      amapAPI: await this.checkAmapAPI(),
      container: await this.checkMapContainer(containerId),
      backendAPI: await this.checkBackendAPI(),
      initialization: false
    };

    // 只有前面的检查都通过才尝试初始化地图
    if (results.amapAPI && results.container) {
      results.initialization = await this.testMapInitialization(containerId);
    }

    const allPassed = Object.values(results).every(Boolean);
    
    console.log(`\n📊 诊断结果: ${allPassed ? '✅ 全部通过' : '❌ 存在问题'}`);
    
    if (!allPassed) {
      console.log('\n🔧 建议解决方案:');
      if (!results.environment) console.log('- 检查环境变量配置');
      if (!results.amapAPI) console.log('- 检查高德地图API加载');
      if (!results.container) console.log('- 检查地图容器设置');
      if (!results.backendAPI) console.log('- 检查后端服务状态');
      if (!results.initialization) console.log('- 检查地图初始化代码');
    }

    this.debugInfo.fullDiagnosis = results;
    console.groupEnd();
    
    return {
      success: allPassed,
      results,
      debugInfo: this.debugInfo
    };
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): any {
    return this.debugInfo;
  }

  /**
   * 清除调试信息
   */
  clearDebugInfo(): void {
    this.debugInfo = {};
  }
}

// 导出单例实例
export const mapDebugger = MapDebugger.getInstance();

// 在开发环境下自动挂载到window对象
if (import.meta.env.DEV) {
  (window as any).mapDebugger = mapDebugger;
}
