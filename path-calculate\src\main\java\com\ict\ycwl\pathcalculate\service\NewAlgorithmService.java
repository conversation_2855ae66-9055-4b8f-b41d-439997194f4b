package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtils;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult;
import com.ict.ycwl.pathcalculate.config.AlgorithmConfig;
import com.ict.ycwl.pathcalculate.pojo.ResultRoute;
import com.ict.ycwl.pathcalculate.service.adapter.DatabaseToAlgorithmAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新算法服务类
 * 提供新算法的完整执行流程，可以被CalculateServiceImpl调用
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-11
 */
@Slf4j
@Service
public class NewAlgorithmService {

    // ===== 日志输出目录配置 =====
    private static final String OUTPUT_DIR = "target/test-results/algorithm/";
    private static final String LOGS_DIR = "target/test-results/algorithm/logs/";

    @Autowired
    private DatabaseToAlgorithmAdapter databaseToAlgorithmAdapter;

    @Autowired
    private PathPlanningUtils pathPlanningUtils;

    @Autowired
    private AlgorithmConfig algorithmConfig;

    @PostConstruct
    public void init() {
        log.info("🚀 NewAlgorithmService初始化检查:");
        log.info("- databaseToAlgorithmAdapter: {}", databaseToAlgorithmAdapter != null ? "✅ 已注入" : "❌ NULL");
        log.info("- pathPlanningUtils: {}", pathPlanningUtils != null ? "✅ 已注入" : "❌ NULL");
        log.info("- algorithmConfig: {}", algorithmConfig != null ? "✅ 已注入" : "❌ NULL");
        if (algorithmConfig != null) {
            log.info("- algorithmConfig.isEnableNewAlgorithm(): {}", algorithmConfig.isEnableNewAlgorithm());
            log.info("- algorithmConfig.isValid(): {}", algorithmConfig.isValid());
        }
    }

    /**
     * 使用新算法执行路径规划
     * 
     * @param apiKey API密钥
     * @return 路径规划结果
     * @throws Exception 执行异常
     */
    public List<ResultRoute> executeNewAlgorithm(String apiKey) throws Exception {
        // ===== 开始详细日志记录 =====
        String sessionId = generateSessionId();
        long startTime = System.currentTimeMillis();

        log.info("========== NewAlgorithm 执行开始 ==========");
        log.info("🎯 会话ID: {}", sessionId);
        log.info("🎯 开始时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        log.info("🎯 API密钥: {}...", apiKey != null ? apiKey.substring(0, Math.min(8, apiKey.length())) : "null");

        // 创建日志输出目录
        createLogDirectories();

        try {
            // 验证配置
            if (!algorithmConfig.isValid()) {
                log.error("❌ 算法配置参数无效");
                saveErrorLog(sessionId, "算法配置参数无效", null);
                throw new IllegalArgumentException("算法配置参数无效");
            }

            // 1. 从数据库加载数据并转换为算法格式
            log.info("📊 步骤1: 从数据库加载数据");
            PathPlanningRequest request = databaseToAlgorithmAdapter.loadDataFromDatabase();
            log.info("📈 数据加载完成统计:");
            log.info("  - 聚集区数量: {}", request.getAccumulations().size());
            log.info("  - 中转站数量: {}", request.getTransitDepots().size());
            log.info("  - 班组数量: {}", request.getTeams().size());
            log.info("  - 时间矩阵记录: {}", request.getTimeMatrix().size());

            // 验证输入数据
            validateInputData(request);
            log.info("✅ 输入数据验证通过");

            // 2. 执行算法
            log.info("📊 步骤2: 执行路径规划算法");
            long algorithmStartTime = System.currentTimeMillis();
            PathPlanningResult algorithmResult = pathPlanningUtils.calculateWithSpring(request);
            long algorithmExecutionTime = System.currentTimeMillis() - algorithmStartTime;

            log.info("⏱️ 算法执行耗时: {}ms", algorithmExecutionTime);

            // 3. 检查算法执行结果
            if (!algorithmResult.isSuccess()) {
                String errorMsg = "路径规划算法执行失败: " + algorithmResult.getErrorMessage();
                log.error("❌ {}", errorMsg);
                saveErrorLog(sessionId, errorMsg, null);
                throw new RuntimeException(errorMsg);
            }

            log.info("🎉 新算法执行成功！");
            log.info("📈 算法结果统计:");
            log.info("  - 生成路线数: {}", algorithmResult.getRoutes().size());
            log.info("  - 算法执行时间: {}ms", algorithmExecutionTime);

            // 4. 转换结果格式
            log.info("📊 步骤3: 转换结果格式");
            long conversionStartTime = System.currentTimeMillis();
            List<ResultRoute> resultRoutes = databaseToAlgorithmAdapter.convertAlgorithmResult(algorithmResult, apiKey);
            long conversionTime = System.currentTimeMillis() - conversionStartTime;

            log.info("✅ 结果转换完成，耗时: {}ms", conversionTime);
            log.info("📈 最终结果统计:");
            log.info("  - 最终路线数: {}", resultRoutes.size());

            // 5. 验证输出结果
            validateOutputData(resultRoutes);
            log.info("✅ 输出数据验证通过");

            // 详细分析结果
            analyzeNewAlgorithmResults(resultRoutes);

            // 记录执行结果
            long totalExecutionTime = System.currentTimeMillis() - startTime;
            log.info("🎉 NewAlgorithm执行完成！");
            log.info("📊 总执行统计:");
            log.info("  - 数据加载+算法执行+结果转换总时间: {}ms", totalExecutionTime);
            log.info("  - 其中算法执行时间: {}ms", algorithmExecutionTime);
            log.info("  - 其中结果转换时间: {}ms", conversionTime);
            log.info("  - 会话ID: {}", sessionId);

            // 保存执行结果到文件
            saveNewAlgorithmResults(sessionId, resultRoutes, totalExecutionTime, algorithmExecutionTime);

            log.info("========== NewAlgorithm 执行结束 ==========");
            return resultRoutes;

        } catch (Exception e) {
            long totalExecutionTime = System.currentTimeMillis() - startTime;
            log.error("❌ NewAlgorithm执行异常，会话ID: {}, 执行时间: {}ms", sessionId, totalExecutionTime, e);
            saveErrorLog(sessionId, "NewAlgorithm执行异常", e);
            throw e; // 重新抛出异常，让调用者处理
        }
    }

    /**
     * 验证输入数据的有效性
     */
    private void validateInputData(PathPlanningRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求对象不能为空");
        }
        
        if (request.getAccumulations() == null || request.getAccumulations().isEmpty()) {
            throw new IllegalArgumentException("聚集区数据不能为空");
        }
        
        if (request.getTransitDepots() == null || request.getTransitDepots().isEmpty()) {
            throw new IllegalArgumentException("中转站数据不能为空");
        }
        
        if (request.getTeams() == null || request.getTeams().isEmpty()) {
            throw new IllegalArgumentException("班组数据不能为空");
        }
        
        // 检查最小数据量要求
        if (request.getAccumulations().size() < algorithmConfig.getParams().getMinAccumulationCount()) {
            throw new IllegalArgumentException("聚集区数量不足，至少需要 " + 
                    algorithmConfig.getParams().getMinAccumulationCount() + " 个");
        }
        
        log.debug("输入数据验证通过");
    }

    /**
     * 验证输出数据的有效性
     */
    private void validateOutputData(List<ResultRoute> resultRoutes) {
        if (resultRoutes == null) {
            throw new RuntimeException("算法结果转换失败，结果为空");
        }
        
        // 检查路线数量限制
        if (resultRoutes.size() > algorithmConfig.getParams().getMaxRouteCount()) {
            log.warn("生成的路线数量 {} 超过了配置的最大值 {}", 
                    resultRoutes.size(), algorithmConfig.getParams().getMaxRouteCount());
        }
        
        // 验证每条路线的基本字段
        for (int i = 0; i < resultRoutes.size(); i++) {
            ResultRoute route = resultRoutes.get(i);
            if (route.getRouteName() == null || route.getRouteName().trim().isEmpty()) {
                throw new RuntimeException("第 " + (i + 1) + " 条路线的名称为空");
            }
            if (route.getTransitDepotId() == null) {
                throw new RuntimeException("第 " + (i + 1) + " 条路线的中转站ID为空");
            }
        }
        
        log.debug("输出数据验证通过");
    }

    /**
     * 检查新算法是否可用
     */
    public boolean isNewAlgorithmAvailable() {
        try {
            log.debug("检查新算法可用性:");
            log.debug("- algorithmConfig: {}", algorithmConfig != null ? "已注入" : "NULL");
            if (algorithmConfig != null) {
                log.debug("- algorithmConfig.isValid(): {}", algorithmConfig.isValid());
                log.debug("- algorithmConfig.isEnableNewAlgorithm(): {}", algorithmConfig.isEnableNewAlgorithm());
            }
            log.debug("- databaseToAlgorithmAdapter: {}", databaseToAlgorithmAdapter != null ? "已注入" : "NULL");
            log.debug("- pathPlanningUtils: {}", pathPlanningUtils != null ? "已注入" : "NULL");

            boolean available = algorithmConfig != null &&
                   algorithmConfig.isValid() &&
                   algorithmConfig.isEnableNewAlgorithm() &&
                   databaseToAlgorithmAdapter != null &&
                   pathPlanningUtils != null;

            log.info("新算法可用性检查结果: {}", available);
            return available;
        } catch (Exception e) {
            log.warn("检查新算法可用性时发生异常", e);
            return false;
        }
    }

    /**
     * 获取算法配置信息
     */
    public AlgorithmConfig getAlgorithmConfig() {
        return algorithmConfig;
    }

    /**
     * 执行算法健康检查
     */
    public boolean healthCheck() {
        try {
            // 检查各个组件
            if (!isNewAlgorithmAvailable()) {
                return false;
            }
            
            // 可以添加更多的健康检查逻辑
            // 比如检查数据库连接、算法组件状态等
            
            return true;
        } catch (Exception e) {
            log.error("算法健康检查失败", e);
            return false;
        }
    }

    // ===== 日志记录辅助方法 =====

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "NEW_ALG_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 创建日志输出目录
     */
    private void createLogDirectories() {
        try {
            Files.createDirectories(Paths.get(OUTPUT_DIR));
            Files.createDirectories(Paths.get(LOGS_DIR));
            log.debug("日志输出目录已创建: {}", OUTPUT_DIR);
        } catch (IOException e) {
            log.error("创建日志目录失败", e);
        }
    }

    /**
     * 分析新算法结果
     */
    private void analyzeNewAlgorithmResults(List<ResultRoute> results) {
        if (results == null || results.isEmpty()) {
            log.warn("⚠️ 新算法结果为空，跳过分析");
            return;
        }

        log.info("📊 开始分析新算法结果...");

        // 工作时间统计
        List<Double> workTimes = new ArrayList<>();
        List<Double> distances = new ArrayList<>();
        List<Double> cargoWeights = new ArrayList<>();
        Map<Long, Integer> routesByDepot = new HashMap<>();

        for (ResultRoute route : results) {
            if (route.getWorkTime() != null) {
                workTimes.add(route.getWorkTime().doubleValue());
            }
            if (route.getDistance() != null) {
                try {
                    distances.add(Double.parseDouble(route.getDistance()));
                } catch (NumberFormatException e) {
                    // 忽略无效距离
                }
            }
            if (route.getCargoWeight() != null) {
                try {
                    cargoWeights.add(Double.parseDouble(route.getCargoWeight()));
                } catch (NumberFormatException e) {
                    // 忽略无效重量
                }
            }

            // 按中转站统计
            Long depotId = route.getTransitDepotId();
            if (depotId != null) {
                routesByDepot.put(depotId, routesByDepot.getOrDefault(depotId, 0) + 1);
            }
        }

        // 工作时间分析
        if (!workTimes.isEmpty()) {
            double avgWorkTime = workTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0);
            double minWorkTime = workTimes.stream().mapToDouble(Double::doubleValue).min().orElse(0);
            double maxWorkTime = workTimes.stream().mapToDouble(Double::doubleValue).max().orElse(0);
            double totalWorkTime = workTimes.stream().mapToDouble(Double::doubleValue).sum();

            log.info("⏰ 工作时间分析:");
            log.info("  - 总工作时间: {:.2f}分钟", totalWorkTime);
            log.info("  - 平均工作时间: {:.2f}分钟", avgWorkTime);
            log.info("  - 最短工作时间: {:.2f}分钟", minWorkTime);
            log.info("  - 最长工作时间: {:.2f}分钟", maxWorkTime);
            log.info("  - 工作时间差: {:.2f}分钟", maxWorkTime - minWorkTime);
        }

        // 距离分析
        if (!distances.isEmpty()) {
            double avgDistance = distances.stream().mapToDouble(Double::doubleValue).average().orElse(0);
            double totalDistance = distances.stream().mapToDouble(Double::doubleValue).sum();

            log.info("🛣️ 距离分析:");
            log.info("  - 总距离: {:.2f}公里", totalDistance);
            log.info("  - 平均距离: {:.2f}公里", avgDistance);
        }

        // 货物重量分析
        if (!cargoWeights.isEmpty()) {
            double avgWeight = cargoWeights.stream().mapToDouble(Double::doubleValue).average().orElse(0);
            double totalWeight = cargoWeights.stream().mapToDouble(Double::doubleValue).sum();

            log.info("📦 货物重量分析:");
            log.info("  - 总重量: {:.2f}公斤", totalWeight);
            log.info("  - 平均重量: {:.2f}公斤", avgWeight);
        }

        // 中转站分布分析
        log.info("🏢 中转站分布:");
        for (Map.Entry<Long, Integer> entry : routesByDepot.entrySet()) {
            log.info("  - 中转站{}: {}条路线", entry.getKey(), entry.getValue());
        }

        log.info("✅ 新算法结果分析完成");
    }

    /**
     * 保存新算法执行结果到文件
     */
    private void saveNewAlgorithmResults(String sessionId, List<ResultRoute> results, long totalTime, long algorithmTime) {
        try {
            // 创建结果摘要
            StringBuilder summary = new StringBuilder();
            summary.append("========== NewAlgorithm 执行结果摘要 ==========\n");
            summary.append("会话ID: ").append(sessionId).append("\n");
            summary.append("执行时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            summary.append("总执行时长: ").append(totalTime).append("ms\n");
            summary.append("算法执行时长: ").append(algorithmTime).append("ms\n");
            summary.append("生成路线数: ").append(results != null ? results.size() : 0).append("\n");
            summary.append("\n");

            if (results != null && !results.isEmpty()) {
                summary.append("========== 路线详细信息 ==========\n");
                for (int i = 0; i < results.size(); i++) {
                    ResultRoute route = results.get(i);
                    summary.append(String.format("路线 %d:\n", i + 1));
                    summary.append("  - 路线名称: ").append(route.getRouteName()).append("\n");
                    summary.append("  - 中转站ID: ").append(route.getTransitDepotId()).append("\n");
                    summary.append("  - 距离: ").append(route.getDistance()).append("km\n");
                    summary.append("  - 货物重量: ").append(route.getCargoWeight()).append("kg\n");
                    summary.append("  - 工作时间: ").append(route.getWorkTime()).append("分钟\n");
                    summary.append("  - 配送时间: ").append(route.getDeliveryTime()).append("分钟\n");
                    summary.append("  - 装货时间: ").append(route.getLoadingTime()).append("分钟\n");
                    summary.append("  - 路径点数: ").append(route.getPolyline() != null ? route.getPolyline().size() : 0).append("\n");
                    summary.append("\n");
                }
            }

            // 保存到文件
            String fileName = String.format("new-algorithm-result-%s.txt", sessionId);
            writeStringToFile(OUTPUT_DIR + fileName, summary.toString());

            log.info("📄 新算法执行结果已保存到文件: {}", fileName);

        } catch (Exception e) {
            log.error("保存新算法结果失败", e);
        }
    }

    /**
     * 保存错误日志
     */
    private void saveErrorLog(String sessionId, String errorMessage, Exception exception) {
        try {
            StringBuilder errorLog = new StringBuilder();
            errorLog.append("========== NewAlgorithm 错误日志 ==========\n");
            errorLog.append("会话ID: ").append(sessionId).append("\n");
            errorLog.append("错误时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            errorLog.append("错误信息: ").append(errorMessage).append("\n");

            if (exception != null) {
                errorLog.append("异常类型: ").append(exception.getClass().getSimpleName()).append("\n");
                errorLog.append("异常消息: ").append(exception.getMessage()).append("\n");
                errorLog.append("堆栈跟踪:\n");
                for (StackTraceElement element : exception.getStackTrace()) {
                    errorLog.append("  ").append(element.toString()).append("\n");
                }
            }

            String fileName = String.format("new-algorithm-error-%s.txt", sessionId);
            writeStringToFile(OUTPUT_DIR + fileName, errorLog.toString());

            log.error("❌ 新算法错误日志已保存到文件: {}", fileName);

        } catch (Exception e) {
            log.error("保存新算法错误日志失败", e);
        }
    }

    /**
     * 写入字符串到文件
     */
    private void writeStringToFile(String filePath, String content) {
        try {
            java.nio.file.Path path = Paths.get(filePath);
            Files.createDirectories(path.getParent());
            try (FileWriter writer = new FileWriter(path.toFile())) {
                writer.write(content);
            }
        } catch (IOException e) {
            log.error("写入文件失败: {}", filePath, e);
        }
    }
}
