-- 查询商铺表中重复坐标的情况

-- 1. 统计重复坐标的组数和总商铺数
SELECT 
    '重复坐标统计' as 统计类型,
    COUNT(*) as 重复坐标组数,
    SUM(商铺数量) as 涉及商铺总数
FROM (
    SELECT 
        longitude, 
        latitude, 
        COUNT(*) as 商铺数量
    FROM store 
    WHERE is_delete = 0 
    GROUP BY longitude, latitude 
    HAVING COUNT(*) > 1
) duplicate_coords;

-- 2. 详细列出所有重复坐标的商铺信息
SELECT 
    longitude as 经度,
    latitude as 纬度,
    COUNT(*) as 商铺数量,
    GROUP_CONCAT(
        CONCAT(customer_code, '(', store_name, '-', store_address, ')') 
        ORDER BY customer_code 
        SEPARATOR ' | '
    ) as 商铺详情
FROM store 
WHERE is_delete = 0 
GROUP BY longitude, latitude 
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC, longitude, latitude;

-- 3. 按重复数量分组统计
SELECT 
    重复数量,
    COUNT(*) as 坐标组数,
    重复数量 * COUNT(*) as 涉及商铺数
FROM (
    SELECT 
        longitude, 
        latitude, 
        COUNT(*) as 重复数量
    FROM store 
    WHERE is_delete = 0 
    GROUP BY longitude, latitude 
    HAVING COUNT(*) > 1
) duplicate_stats
GROUP BY 重复数量
ORDER BY 重复数量 DESC;

-- 4. 特别关注B07188和B07049的情况
SELECT 
    customer_code as 商铺编码,
    store_name as 商铺名称,
    store_address as 商铺地址,
    longitude as 经度,
    latitude as 纬度,
    accumulation_id as 聚集区ID,
    area_name as 大区名称
FROM store 
WHERE customer_code IN ('B07188', 'B07049') 
   AND is_delete = 0
ORDER BY customer_code;

-- 5. 查找坐标为 113.310826, 25.013573 的所有商铺
SELECT 
    customer_code as 商铺编码,
    store_name as 商铺名称,
    store_address as 商铺地址,
    longitude as 经度,
    latitude as 纬度,
    accumulation_id as 聚集区ID,
    area_name as 大区名称,
    is_special as 是否特殊点,
    special_type as 特殊点类型,
    remark as 备注
FROM store 
WHERE longitude = 113.310826 
   AND latitude = 25.013573 
   AND is_delete = 0
ORDER BY customer_code;
