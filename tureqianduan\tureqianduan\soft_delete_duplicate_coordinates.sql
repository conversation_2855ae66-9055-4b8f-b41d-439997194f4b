-- 软删除重复坐标的商铺记录（更安全的方式）
-- 策略：对于每个重复的坐标，保留store_id最小的记录，将其他记录标记为删除

-- 首先查看重复坐标的情况
SELECT 
    '执行软删除前的重复坐标统计' as 说明,
    COUNT(*) as 重复坐标组数,
    SUM(商铺数量) as 涉及商铺总数,
    SUM(商铺数量) - COUNT(*) as 将要软删除的商铺数
FROM (
    SELECT 
        longitude, 
        latitude, 
        COUNT(*) as 商铺数量
    FROM store 
    WHERE is_delete = 0 
    GROUP BY longitude, latitude 
    HAVING COUNT(*) > 1
) duplicate_coords;

-- 查看将要被软删除的具体记录
SELECT 
    s1.store_id,
    s1.customer_code,
    s1.store_address,
    s1.longitude,
    s1.latitude,
    '将被软删除' as 状态
FROM store s1
INNER JOIN store s2 
WHERE s1.longitude = s2.longitude 
  AND s1.latitude = s2.latitude
  AND s1.store_id > s2.store_id  -- 将要删除store_id较大的记录
  AND s1.is_delete = 0 
  AND s2.is_delete = 0
ORDER BY s1.longitude, s1.latitude, s1.store_id;

-- 查看将要保留的记录
SELECT 
    s.store_id,
    s.customer_code,
    s.store_address,
    s.longitude,
    s.latitude,
    '将被保留' as 状态
FROM store s
WHERE s.is_delete = 0
  AND s.store_id IN (
    SELECT MIN(store_id) 
    FROM store 
    WHERE is_delete = 0 
    GROUP BY longitude, latitude 
    HAVING COUNT(*) > 1
  )
ORDER BY s.longitude, s.latitude, s.store_id;

-- ==========================================
-- 软删除操作（推荐使用这个，更安全）
-- ==========================================

-- 软删除重复坐标的商铺（保留每个坐标位置store_id最小的记录）
UPDATE store s1
INNER JOIN store s2 
SET s1.is_delete = 1,
    s1.update_time = NOW()
WHERE s1.longitude = s2.longitude 
  AND s1.latitude = s2.latitude
  AND s1.store_id > s2.store_id  -- 软删除store_id较大的记录
  AND s1.is_delete = 0 
  AND s2.is_delete = 0;

-- 验证软删除结果
SELECT 
    '软删除后的重复坐标统计' as 说明,
    COUNT(*) as 重复坐标组数
FROM (
    SELECT 
        longitude, 
        latitude, 
        COUNT(*) as 商铺数量
    FROM store 
    WHERE is_delete = 0 
    GROUP BY longitude, latitude 
    HAVING COUNT(*) > 1
) remaining_duplicates;

-- 查看软删除后的统计
SELECT 
    '软删除统计' as 类型,
    COUNT(*) as 被软删除的商铺数
FROM store 
WHERE is_delete = 1 
  AND update_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR);  -- 最近1小时内被软删除的

-- 查看坐标 113.310826, 25.013573 软删除后的情况
SELECT 
    store_id,
    customer_code as 商铺编码,
    store_address as 商铺地址,
    is_delete as 删除状态,
    CASE WHEN is_delete = 0 THEN '保留' ELSE '已删除' END as 状态说明
FROM store 
WHERE longitude = 113.310826 
   AND latitude = 25.013573
ORDER BY store_id;

-- 如果需要恢复某些被误删的记录，可以使用以下语句：
-- UPDATE store SET is_delete = 0, update_time = NOW() WHERE customer_code = 'B07188' AND is_delete = 1;
